using Microsoft.Web.WebView2.Core;

namespace YouTubePlayerApp;

/// <summary>
/// Optimized controls for video quality settings and enforces quality restrictions on YouTube
/// </summary>
public sealed class VideoQualityController : IDisposable
{
    private CoreWebView2? _webView;
    private readonly Dictionary<string, string> _qualityMappings;
    private readonly SettingsCache _settingsCache;
    private System.Timers.Timer? _qualityMonitorTimer;
    private bool _disposed;
    private DateTime _lastQualityCheck = DateTime.MinValue;
    private readonly TimeSpan _minCheckInterval = TimeSpan.FromSeconds(5); // Reduced frequency

    public VideoQualityController()
    {
        _settingsCache = SettingsCache.Instance;
        _qualityMappings = InitializeQualityMappings();
    }

    public void Initialize(CoreWebView2 webView)
    {
        if (_disposed) return;
        
        _webView = webView;
        
        PerformanceMonitor.Instance.MeasureOperation("VideoQualityController.Initialize", () =>
        {
            if (_settingsCache.ForceVideoQuality)
            {
                EnableQualityControl();
            }
        });
    }

    public void EnableQualityControl()
    {
        if (_webView == null) return;

        // Set up event handlers
        _webView.DOMContentLoaded += OnDOMContentLoaded;
        _webView.NavigationCompleted += OnNavigationCompleted;

        // Start continuous monitoring if override is enabled
        if (AppSettings.OverrideUserQualityChanges)
        {
            StartQualityMonitoring();
        }
    }

    public void DisableQualityControl()
    {
        if (_webView == null) return;

        _webView.DOMContentLoaded -= OnDOMContentLoaded;
        _webView.NavigationCompleted -= OnNavigationCompleted;
        
        StopQualityMonitoring();
    }

    private Dictionary<string, string> InitializeQualityMappings()
    {
        return new Dictionary<string, string>
        {
            ["auto"] = "auto",
            ["144p"] = "tiny",
            ["240p"] = "small", 
            ["360p"] = "medium",
            ["480p"] = "large",
            ["720p"] = "hd720",
            ["1080p"] = "hd1080",
            ["1440p"] = "hd1440",
            ["2160p"] = "hd2160"
        };
    }

    private async void OnDOMContentLoaded(object? sender, CoreWebView2DOMContentLoadedEventArgs e)
    {
        if (_webView == null) return;

        try
        {
            // Inject quality control initialization script
            await InjectQualityControlScript();
            
            // Hide quality controls if requested
            if (AppSettings.HideQualityControls)
            {
                await HideQualityControls();
            }
        }
        catch (Exception ex)
        {
            AppLogger.Instance.LogError("VideoQualityController", ex, "Error in OnDOMContentLoaded");
        }
    }

    private async void OnNavigationCompleted(object? sender, CoreWebView2NavigationCompletedEventArgs e)
    {
        if (_webView == null || !e.IsSuccess) return;

        try
        {
            // Apply quality settings after navigation
            await ApplyQualitySettings();
        }
        catch (Exception ex)
        {
            AppLogger.Instance.LogError("VideoQualityController", ex, "Error in OnNavigationCompleted");
        }
    }

    private async Task InjectQualityControlScript()
    {
        if (_webView == null) return;

        var forcedQuality = _qualityMappings.GetValueOrDefault(AppSettings.ForcedVideoQuality, "auto");
        var lockControls = AppSettings.LockQualityControls.ToString().ToLower();
        var hideControls = AppSettings.HideQualityControls.ToString().ToLower();
        var overrideChanges = AppSettings.OverrideUserQualityChanges.ToString().ToLower();

        var script = $@"
            (function() {{
                'use strict';
                
                if (window.qualityControllerInjected) {{
                    console.log('[QualityController] Already injected, skipping');
                    return;
                }}
                window.qualityControllerInjected = true;
                
                // Configuration
                const FORCED_QUALITY = '{forcedQuality}';
                const LOCK_CONTROLS = {lockControls};
                const HIDE_CONTROLS = {hideControls};
                const OVERRIDE_CHANGES = {overrideChanges};
                
                let currentPlayer = null;
                let qualitySetAttempts = 0;
                const MAX_ATTEMPTS = 150; // Increased attempts
                let intervalId = null;
                let isQualityApplied = false;
                let lastAppliedQuality = null;
                let playerReadyCallbacks = [];
                
                console.log('[QualityController] YouTube Quality Controller 2024 activated');
                console.log('[QualityController] Target Quality:', FORCED_QUALITY);
                console.log('[QualityController] Lock Controls:', LOCK_CONTROLS);
                console.log('[QualityController] Hide Controls:', HIDE_CONTROLS);
                console.log('[QualityController] Override Changes:', OVERRIDE_CHANGES);
                
                // Updated quality mapping for current YouTube (2024)
                const qualityMap = {{
                    'tiny': '144p',
                    'small': '240p', 
                    'medium': '360p',
                    'large': '480p',
                    'hd720': '720p',
                    'hd1080': '1080p',
                    'hd1440': '1440p',
                    'hd2160': '2160p',
                    'auto': 'Auto'
                }};
                
                // Reverse mapping for API calls
                const apiQualityMap = {{
                    '144p': 'tiny',
                    '240p': 'small',
                    '360p': 'medium',
                    '480p': 'large',
                    '720p': 'hd720',
                    '1080p': 'hd1080',
                    '1440p': 'hd1440',
                    '2160p': 'hd2160'
                }};
                
                // Enhanced player detection with multiple strategies
                function waitForPlayer() {{
                    console.log('[QualityController] Starting player detection...');
                    
                    const checkPlayer = () => {{
                        qualitySetAttempts++;
                        
                        // Strategy 1: Look for YouTube player elements
                        const playerElement = document.getElementById('movie_player') || 
                                            document.querySelector('.html5-video-player') ||
                                            document.querySelector('#player-container .html5-video-player') ||
                                            document.querySelector('ytd-player #container .html5-video-player');
                        
                        // Strategy 2: Check for video elements
                        const videoElement = document.querySelector('video');
                        
                        // Strategy 3: Check for YouTube API availability
                        const hasYTAPI = window.yt && (window.yt.player || window.ytplayer);
                        
                        // Strategy 4: Check for player ready state
                        const isPlayerReady = playerElement && (
                            typeof playerElement.getAvailableQualityLevels === 'function' ||
                            typeof playerElement.setPlaybackQuality === 'function'
                        );
                        
                        console.log('[QualityController] Detection attempt', qualitySetAttempts, ':', {{
                            playerElement: !!playerElement,
                            videoElement: !!videoElement,
                            hasYTAPI: !!hasYTAPI,
                            isPlayerReady: !!isPlayerReady
                        }});
                        
                        if (playerElement || (videoElement && hasYTAPI)) {{
                            currentPlayer = playerElement || videoElement;
                            console.log('[QualityController] Player detected successfully');
                            
                            // Wait a bit more for full initialization
                            setTimeout(() => {{
                                initializeQualityControl();
                            }}, 1000);
                            return true;
                        }}
                        
                        if (qualitySetAttempts < MAX_ATTEMPTS) {{
                            setTimeout(checkPlayer, 200); // Slower but more reliable
                        }} else {{
                            console.log('[QualityController] Max attempts reached, trying advanced fallback');
                            tryAdvancedFallback();
                        }}
                        return false;
                    }};
                    
                    checkPlayer();
                }}
                
                // Advanced fallback methods for stubborn cases
                function tryAdvancedFallback() {{
                    console.log('[QualityController] Executing advanced fallback strategies');
                    
                    // Strategy 1: Hook into YouTube's player creation
                    if (window.yt && window.yt.player) {{
                        const originalGetPlayerByElement = window.yt.player.getPlayerByElement;
                        if (originalGetPlayerByElement) {{
                            window.yt.player.getPlayerByElement = function(element) {{
                                const player = originalGetPlayerByElement.call(this, element);
                                if (player && !isQualityApplied) {{
                                    console.log('[QualityController] Hooked into player creation');
                                    currentPlayer = player;
                                    setTimeout(() => applyQualityViaAPI(player), 500);
                                }}
                                return player;
                            }};
                        }}
                    }}
                    
                    // Strategy 2: Monitor for video elements with metadata
                    const videoObserver = new MutationObserver((mutations) => {{
                        mutations.forEach((mutation) => {{
                            mutation.addedNodes.forEach((node) => {{
                                if (node.nodeType === 1) {{
                                    const video = node.tagName === 'VIDEO' ? node : node.querySelector && node.querySelector('video');
                                    if (video && video.readyState >= 1 && !isQualityApplied) {{
                                        console.log('[QualityController] Found video with metadata via observer');
                                        setTimeout(() => applyQualityViaVideoElement(video), 1000);
                                    }}
                                }}
                            }});
                        }});
                    }});
                    
                    videoObserver.observe(document.body, {{ childList: true, subtree: true }});
                    
                    // Strategy 3: Periodic DOM manipulation attempts
                    let fallbackAttempts = 0;
                    const fallbackInterval = setInterval(() => {{
                        if (isQualityApplied || fallbackAttempts > 20) {{
                            clearInterval(fallbackInterval);
                            return;
                        }}
                        
                        fallbackAttempts++;
                        console.log('[QualityController] Fallback DOM attempt', fallbackAttempts);
                        applyQualityViaDOMManipulation();
                    }}, 2000);
                }}
                
                // Enhanced video element quality application
                function applyQualityViaVideoElement(video) {{
                    if (FORCED_QUALITY === 'auto' || isQualityApplied) return;
                    
                    console.log('[QualityController] Applying quality via video element');
                    
                    // Wait for video to be fully ready
                    const applyWhenReady = () => {{
                        if (video.readyState >= 2) {{ // HAVE_CURRENT_DATA
                            setTimeout(() => {{
                                applyQualitySettings();
                                applyQualityViaDOMManipulation();
                            }}, 1000);
                        }} else {{
                            video.addEventListener('canplay', () => {{
                                setTimeout(() => {{
                                    applyQualitySettings();
                                    applyQualityViaDOMManipulation();
                                }}, 1000);
                            }}, {{ once: true }});
                        }}
                    }};
                    
                    applyWhenReady();
                }}
                
                // Enhanced API-based quality application
                function applyQualityViaAPI(playerAPI) {{
                    if (FORCED_QUALITY === 'auto' || isQualityApplied) return;
                    
                    console.log('[QualityController] Applying quality via API');
                    
                    try {{
                        // Method 1: Direct quality setting
                        if (typeof playerAPI.setPlaybackQuality === 'function') {{
                            playerAPI.setPlaybackQuality(FORCED_QUALITY);
                            console.log('[QualityController] Quality set via setPlaybackQuality:', FORCED_QUALITY);
                            isQualityApplied = true;
                            lastAppliedQuality = FORCED_QUALITY;
                        }}
                        
                        // Method 2: Quality range setting
                        if (typeof playerAPI.setPlaybackQualityRange === 'function') {{
                            playerAPI.setPlaybackQualityRange(FORCED_QUALITY, FORCED_QUALITY);
                            console.log('[QualityController] Quality range set:', FORCED_QUALITY);
                        }}
                        
                        // Method 3: Try alternative API methods
                        if (typeof playerAPI.setQuality === 'function') {{
                            playerAPI.setQuality(FORCED_QUALITY);
                            console.log('[QualityController] Quality set via setQuality:', FORCED_QUALITY);
                        }}
                        
                    }} catch (error) {{
                        console.error('[QualityController] Error applying quality via API:', error);
                        // Fallback to DOM manipulation
                        setTimeout(() => applyQualityViaDOMManipulation(), 500);
                    }}
                }}
                
                // Enhanced quality control initialization
                function initializeQualityControl() {{
                    console.log('[QualityController] Initializing quality control...');
                    
                    try {{
                        let success = false;
                        
                        // Method 1: YouTube Player API (most reliable)
                        if (currentPlayer && typeof currentPlayer.getAvailableQualityLevels === 'function') {{
                            console.log('[QualityController] Using YouTube Player API');
                            success = applyQualitySettings();
                        }}
                        
                        // Method 2: Global YouTube API
                        if (!success && window.yt) {{
                            console.log('[QualityController] Using global YouTube API');
                            applyQualityViaYTGlobal();
                        }}
                        
                        // Method 3: DOM manipulation (fallback)
                        if (!success) {{
                            console.log('[QualityController] Using DOM manipulation fallback');
                            setTimeout(() => applyQualityViaDOMManipulation(), 1500);
                        }}
                        
                        // Apply UI modifications
                        if (LOCK_CONTROLS || HIDE_CONTROLS) {{
                            setTimeout(() => modifyQualityControls(), 2000);
                        }}
                        
                        // Set up continuous monitoring
                        if (OVERRIDE_CHANGES) {{
                            setupQualityOverride();
                        }}
                        
                        // Set up video change monitoring
                        setupVideoChangeMonitoring();
                        
                    }} catch (error) {{
                        console.error('[QualityController] Error initializing quality control:', error);
                        // Retry after delay
                        setTimeout(() => {{
                            console.log('[QualityController] Retrying initialization...');
                            initializeQualityControl();
                        }}, 2000);
                    }}
                }}
                
                // Enhanced quality settings application
                function applyQualitySettings() {{
                    if (!currentPlayer || FORCED_QUALITY === 'auto') return false;
                    
                    console.log('[QualityController] Applying quality settings...');
                    
                    try {{
                        // Check if player has quality methods
                        if (typeof currentPlayer.getAvailableQualityLevels === 'function') {{
                            const availableQualities = currentPlayer.getAvailableQualityLevels();
                            console.log('[QualityController] Available qualities:', availableQualities);
                            
                            if (availableQualities && availableQualities.length > 0) {{
                                // Try exact match first
                                if (availableQualities.includes(FORCED_QUALITY)) {{
                                    currentPlayer.setPlaybackQuality(FORCED_QUALITY);
                                    console.log('[QualityController] Quality set to:', FORCED_QUALITY);
                                    isQualityApplied = true;
                                    lastAppliedQuality = FORCED_QUALITY;
                                    return true;
                                }}
                                
                                // Try to find closest quality
                                const targetQuality = findClosestQuality(FORCED_QUALITY, availableQualities);
                                if (targetQuality) {{
                                    currentPlayer.setPlaybackQuality(targetQuality);
                                    console.log('[QualityController] Closest quality set to:', targetQuality);
                                    isQualityApplied = true;
                                    lastAppliedQuality = targetQuality;
                                    return true;
                                }}
                            }}
                        }}
                        
                        // Try alternative methods
                        if (typeof currentPlayer.setPlaybackQuality === 'function') {{
                            currentPlayer.setPlaybackQuality(FORCED_QUALITY);
                            console.log('[QualityController] Quality set via direct method:', FORCED_QUALITY);
                            isQualityApplied = true;
                            lastAppliedQuality = FORCED_QUALITY;
                            return true;
                        }}
                        
                    }} catch (error) {{
                        console.error('[QualityController] Error in applyQualitySettings:', error);
                    }}
                    
                    return false;
                }}
                
                // Find closest available quality
                function findClosestQuality(targetQuality, availableQualities) {{
                    const qualityOrder = ['tiny', 'small', 'medium', 'large', 'hd720', 'hd1080', 'hd1440', 'hd2160'];
                    const targetIndex = qualityOrder.indexOf(targetQuality);
                    
                    if (targetIndex === -1) return availableQualities[0];
                    
                    // Look for closest quality
                    for (let i = 0; i < qualityOrder.length; i++) {{
                        const checkQuality = qualityOrder[Math.abs(targetIndex - i)] || qualityOrder[targetIndex + i];
                        if (checkQuality && availableQualities.includes(checkQuality)) {{
                            return checkQuality;
                        }}
                    }}
                    
                    return availableQualities[0];
                }}
                
                // Enhanced global YouTube API application
                function applyQualityViaYTGlobal() {{
                    if (FORCED_QUALITY === 'auto') return;
                    
                    console.log('[QualityController] Applying quality via global YouTube API');
                    
                    try {{
                        // Method 1: Hook into YouTube's player initialization
                        if (window.yt && window.yt.config_) {{
                            // Set default quality in config
                            if (window.yt.config_.EXPERIMENT_FLAGS) {{
                                window.yt.config_.EXPERIMENT_FLAGS.html5_default_quality = FORCED_QUALITY;
                                window.yt.config_.EXPERIMENT_FLAGS.html5_disable_av1_hdr = true;
                                console.log('[QualityController] Set quality in YT config flags');
                            }}
                        }}
                        
                        // Method 2: Access player through global variables
                        if (window.ytplayer && window.ytplayer.config) {{
                            const playerId = window.ytplayer.config.player_id;
                            if (playerId) {{
                                const playerElement = document.getElementById(playerId);
                                if (playerElement && typeof playerElement.setPlaybackQuality === 'function') {{
                                    playerElement.setPlaybackQuality(FORCED_QUALITY);
                                    console.log('[QualityController] Quality set via global API:', FORCED_QUALITY);
                                    isQualityApplied = true;
                                    lastAppliedQuality = FORCED_QUALITY;
                                }}
                            }}
                        }}
                        
                        // Method 3: Try to find player via YouTube's internal API
                        if (window.yt && window.yt.player && window.yt.player.getPlayerByElement) {{
                            const containers = document.querySelectorAll('#player-container, #movie_player, .html5-video-player');
                            containers.forEach(container => {{
                                try {{
                                    const player = window.yt.player.getPlayerByElement(container);
                                    if (player && typeof player.setPlaybackQuality === 'function') {{
                                        player.setPlaybackQuality(FORCED_QUALITY);
                                        console.log('[QualityController] Quality set via YT internal API:', FORCED_QUALITY);
                                        isQualityApplied = true;
                                        lastAppliedQuality = FORCED_QUALITY;
                                    }}
                                }} catch (e) {{
                                    // Continue to next container
                                }}
                            }});
                        }}
                        
                    }} catch (error) {{
                        console.error('[QualityController] Error in applyQualityViaYTGlobal:', error);
                    }}
                }}
                
                // Enhanced DOM manipulation with modern selectors
                function applyQualityViaDOMManipulation() {{
                    if (FORCED_QUALITY === 'auto' || isQualityApplied) return;
                    
                    console.log('[QualityController] Attempting enhanced DOM manipulation');
                    
                    // Wait for UI to be ready
                    setTimeout(() => {{
                        try {{
                            // Updated selectors for current YouTube (2024)
                            const settingsButton = document.querySelector('.ytp-settings-button') ||
                                                 document.querySelector('[data-tooltip-target-id=""ytp-settings-button""]') ||
                                                 document.querySelector('.ytp-chrome-controls .ytp-button[aria-label*=""Settings""]');
                            
                            if (!settingsButton) {{
                                console.log('[QualityController] Settings button not found');
                                return;
                            }}
                            
                            console.log('[QualityController] Found settings button, attempting click');
                            
                            // Ensure button is visible and clickable
                            if (settingsButton.offsetParent === null) {{
                                console.log('[QualityController] Settings button not visible');
                                return;
                            }}
                            
                            // Simulate click to open settings
                            settingsButton.click();
                            
                            setTimeout(() => {{
                                // Look for quality menu item with updated selectors
                                const qualityMenuItem = document.querySelector('.ytp-menuitem[aria-haspopup=""true""]') ||
                                                      document.querySelector('.ytp-menuitem:has(.ytp-menuitem-label)') ||
                                                      Array.from(document.querySelectorAll('.ytp-menuitem')).find(item => 
                                                          item.textContent && item.textContent.toLowerCase().includes('quality')
                                                      );
                                
                                if (qualityMenuItem) {{
                                    console.log('[QualityController] Found quality menu item');
                                    qualityMenuItem.click();
                                    
                                    setTimeout(() => {{
                                        // Look for the desired quality option with enhanced detection
                                        const targetQuality = qualityMap[FORCED_QUALITY] || FORCED_QUALITY;
                                        console.log('[QualityController] Looking for quality:', targetQuality);
                                        
                                        // Multiple selector strategies for quality options
                                        const qualityOptions = [
                                            ...document.querySelectorAll('.ytp-menuitem-label'),
                                            ...document.querySelectorAll('.ytp-menuitem-content'),
                                            ...document.querySelectorAll('.ytp-menuitem')
                                        ];
                                        
                                        let qualitySet = false;
                                        for (const option of qualityOptions) {{
                                            const optionText = option.textContent || option.innerText || '';
                                            
                                            // Check for exact match or partial match
                                            if (optionText.includes(targetQuality) || 
                                                optionText.includes(targetQuality.replace('p', '')) ||
                                                (targetQuality === 'auto' && optionText.toLowerCase().includes('auto'))) {{
                                                
                                                console.log('[QualityController] Found matching quality option:', optionText);
                                                option.click();
                                                isQualityApplied = true;
                                                lastAppliedQuality = FORCED_QUALITY;
                                                qualitySet = true;
                                                
                                                // Close the menu
                                                setTimeout(() => {{
                                                    document.body.click();
                                                }}, 100);
                                                break;
                                            }}
                                        }}
                                        
                                        if (!qualitySet) {{
                                            console.log('[QualityController] Target quality not found in options');
                                            // Close menu anyway
                                            document.body.click();
                                        }}
                                        
                                    }}, 300); // Increased delay for menu to appear
                                }} else {{
                                    console.log('[QualityController] Quality menu item not found');
                                    // Close settings menu
                                    document.body.click();
                                }}
                            }}, 300); // Increased delay for settings menu to appear
                            
                        }} catch (error) {{
                            console.error('[QualityController] Error in DOM manipulation:', error);
                            // Try to close any open menus
                            try {{
                                document.body.click();
                            }} catch (e) {{
                                // Ignore cleanup errors
                            }}
                        }}
                    }}, 1500); // Increased initial delay
                }}
                
                // Enhanced video change monitoring
                function setupVideoChangeMonitoring() {{
                    console.log('[QualityController] Setting up video change monitoring');
                    
                    // Monitor for URL changes (navigation)
                    let lastUrl = window.location.href;
                    const urlObserver = new MutationObserver(() => {{
                        if (window.location.href !== lastUrl) {{
                            lastUrl = window.location.href;
                            console.log('[QualityController] URL changed to:', lastUrl);
                            isQualityApplied = false;
                            lastAppliedQuality = null;
                            
                            // Reset and reinitialize after navigation
                            setTimeout(() => {{
                                console.log('[QualityController] Reinitializing after navigation');
                                waitForPlayer();
                            }}, 2000); // Longer delay for navigation
                        }}
                    }});
                    
                    urlObserver.observe(document.body, {{ childList: true, subtree: true }});
                    
                    // Monitor for video element changes
                    const videoObserver = new MutationObserver((mutations) => {{
                        let videoChanged = false;
                        let newVideoFound = false;
                        
                        mutations.forEach((mutation) => {{
                            if (mutation.addedNodes.length > 0) {{
                                const hasVideo = Array.from(mutation.addedNodes).some(node => {{
                                    if (node.nodeType === 1) {{
                                        if (node.tagName === 'VIDEO') {{
                                            newVideoFound = true;
                                            return true;
                                        }}
                                        if (node.querySelector && node.querySelector('video')) {{
                                            newVideoFound = true;
                                            return true;
                                        }}
                                        // Check for player container changes
                                        if (node.classList && (
                                            node.classList.contains('html5-video-player') ||
                                            node.classList.contains('video-stream') ||
                                            node.id === 'movie_player'
                                        )) {{
                                            videoChanged = true;
                                            return true;
                                        }}
                                    }}
                                    return false;
                                }});
                                
                                if (hasVideo) {{
                                    videoChanged = true;
                                }}
                            }}
                        }});
                        
                        if (videoChanged || newVideoFound) {{
                            console.log('[QualityController] Video element changed, newVideo:', newVideoFound);
                            isQualityApplied = false;
                            lastAppliedQuality = null;
                            
                            setTimeout(() => {{
                                console.log('[QualityController] Reinitializing after video change');
                                initializeQualityControl();
                            }}, 1000);
                        }}
                    }});
                    
                    videoObserver.observe(document.body, {{ childList: true, subtree: true }});
                    
                    // Monitor for player state changes
                    const stateObserver = new MutationObserver(() => {{
                        const video = document.querySelector('video');
                        if (video && !isQualityApplied && video.readyState >= 1) {{
                            console.log('[QualityController] Video ready state changed, attempting quality application');
                            setTimeout(() => initializeQualityControl(), 500);
                        }}
                    }});
                    
                    stateObserver.observe(document.body, {{ attributes: true, subtree: true }});
                }}
                
                // Enhanced quality controls modification
                function modifyQualityControls() {{
                    console.log('[QualityController] Modifying quality controls');
                    
                    const modifyControlsFunction = () => {{
                        if (HIDE_CONTROLS) {{
                            // Hide settings button entirely
                            const settingsButtons = document.querySelectorAll('.ytp-settings-button, [data-tooltip-target-id=""ytp-settings-button""]');
                            settingsButtons.forEach(btn => {{
                                btn.style.display = 'none !important';
                                btn.style.visibility = 'hidden !important';
                                btn.style.opacity = '0 !important';
                            }});
                            
                            // Also hide any quality-related menu items if they appear
                            const qualityMenuItems = document.querySelectorAll('.ytp-menuitem');
                            qualityMenuItems.forEach(item => {{
                                if (item.textContent && item.textContent.toLowerCase().includes('quality')) {{
                                    item.style.display = 'none !important';
                                }}
                            }});
                            
                        }} else if (LOCK_CONTROLS) {{
                            // Disable quality menu interactions but keep visible
                            const settingsButtons = document.querySelectorAll('.ytp-settings-button');
                            settingsButtons.forEach(btn => {{
                                btn.style.opacity = '0.5';
                                btn.style.pointerEvents = 'none';
                                btn.title = 'Quality controls are locked';
                            }});
                            
                            // Disable quality menu items
                            const qualityMenuItems = document.querySelectorAll('.ytp-menuitem');
                            qualityMenuItems.forEach(item => {{
                                if (item.textContent && item.textContent.toLowerCase().includes('quality')) {{
                                    item.style.pointerEvents = 'none';
                                    item.style.opacity = '0.5';
                                    item.style.cursor = 'not-allowed';
                                }}
                            }});
                        }}
                    }};
                    
                    // Apply immediately
                    modifyControlsFunction();
                    
                    // Apply periodically to catch dynamic content
                    setInterval(modifyControlsFunction, 3000);
                    
                    // Apply when DOM changes
                    const controlObserver = new MutationObserver(modifyControlsFunction);
                    controlObserver.observe(document.body, {{ childList: true, subtree: true }});
                }}
                
                // Enhanced continuous quality override
                function setupQualityOverride() {{
                    if (!OVERRIDE_CHANGES || FORCED_QUALITY === 'auto') return;
                    
                    console.log('[QualityController] Setting up enhanced quality override monitoring');
                    
                    // Clear any existing interval
                    if (intervalId) clearInterval(intervalId);
                    
                    // Monitor every 4 seconds (balanced between responsiveness and performance)
                    intervalId = setInterval(() => {{
                        try {{
                            if (!isQualityApplied) {{
                                console.log('[QualityController] Quality not applied, retrying initialization...');
                                initializeQualityControl();
                            }} else {{
                                // Check if quality has been changed by user or YouTube
                                const player = document.getElementById('movie_player') || 
                                              document.querySelector('.html5-video-player');
                                
                                if (player && typeof player.getPlaybackQuality === 'function') {{
                                    try {{
                                        const currentQuality = player.getPlaybackQuality();
                                        if (currentQuality && currentQuality !== FORCED_QUALITY && currentQuality !== lastAppliedQuality) {{
                                            console.log('[QualityController] Quality drift detected:', currentQuality, '!==', FORCED_QUALITY, '- reverting');
                                            
                                            // Reapply the forced quality
                                            player.setPlaybackQuality(FORCED_QUALITY);
                                            lastAppliedQuality = FORCED_QUALITY;
                                            
                                            // Also try DOM manipulation as backup
                                            setTimeout(() => applyQualityViaDOMManipulation(), 1000);
                                        }}
                                    }} catch (qualityCheckError) {{
                                        console.log('[QualityController] Error checking current quality:', qualityCheckError);
                                    }}
                                }}
                                
                                // Additional check: ensure video element hasn't changed
                                const video = document.querySelector('video');
                                if (video && video.readyState >= 1) {{
                                    // Video is ready but quality might not be applied
                                    const videoSrc = video.currentSrc || video.src;
                                    if (videoSrc && !videoSrc.includes('quality=' + FORCED_QUALITY)) {{
                                        console.log('[QualityController] Video source doesn\'t reflect forced quality, reapplying...');
                                        applyQualitySettings();
                                    }}
                                }}
                            }}
                        }} catch (overrideError) {{
                            console.error('[QualityController] Error in quality override:', overrideError);
                        }}
                    }}, 4000);
                    
                    // Also monitor for specific YouTube events
                    if (window.yt && window.yt.pubsub) {{
                        try {{
                            // Listen for quality change events
                            window.yt.pubsub.subscribe('onPlaybackQualityChange', (quality) => {{
                                if (quality && quality !== FORCED_QUALITY) {{
                                    console.log('[QualityController] YouTube quality change event detected:', quality, '- reverting to:', FORCED_QUALITY);
                                    setTimeout(() => {{
                                        applyQualitySettings();
                                    }}, 500);
                                }}
                            }});
                        }} catch (pubsubError) {{
                            console.log('[QualityController] Could not subscribe to YouTube events:', pubsubError);
                        }}
                    }}
                }}
                
                // Enhanced initialization with multiple triggers
                function initializeController() {{
                    console.log('[QualityController] Starting controller initialization...');
                    setTimeout(() => {{
                        waitForPlayer();
                    }}, 1000); // Longer initial delay for better reliability
                }}
                
                // Initialize when page is ready
                if (document.readyState === 'loading') {{
                    document.addEventListener('DOMContentLoaded', initializeController);
                }} else if (document.readyState === 'interactive') {{
                    // DOM is ready but resources might still be loading
                    setTimeout(initializeController, 1500);
                }} else {{
                    // Document is fully loaded
                    initializeController();
                }}
                
                // Also initialize on visibility change (for tab switching)
                document.addEventListener('visibilitychange', () => {{
                    if (!document.hidden) {{
                        console.log('[QualityController] Tab became visible');
                        if (!isQualityApplied) {{
                            setTimeout(() => {{
                                console.log('[QualityController] Reinitializing on tab focus');
                                waitForPlayer();
                            }}, 1500);
                        }}
                    }}
                }});
                
                // Initialize on window focus
                window.addEventListener('focus', () => {{
                    console.log('[QualityController] Window focused');
                    if (!isQualityApplied) {{
                        setTimeout(() => {{
                            console.log('[QualityController] Reinitializing on window focus');
                            waitForPlayer();
                        }}, 1000);
                    }}
                }});
                
                // Listen for YouTube's own ready events
                if (window.yt) {{
                    // Hook into YouTube's navigation system
                    const originalNavigate = window.yt.navigate;
                    if (originalNavigate) {{
                        window.yt.navigate = function(...args) {{
                            console.log('[QualityController] YouTube navigation detected');
                            isQualityApplied = false;
                            lastAppliedQuality = null;
                            
                            const result = originalNavigate.apply(this, args);
                            
                            setTimeout(() => {{
                                console.log('[QualityController] Reinitializing after YT navigation');
                                waitForPlayer();
                            }}, 2000);
                            
                            return result;
                        }};
                    }}
                }}
                
                // Listen for video load events
                window.addEventListener('yt-navigate-finish', () => {{
                    console.log('[QualityController] YouTube navigation finished');
                    isQualityApplied = false;
                    lastAppliedQuality = null;
                    setTimeout(() => {{
                        console.log('[QualityController] Reinitializing after navigation finish');
                        waitForPlayer();
                    }}, 1500);
                }});
                
                // Backup initialization timer
                setTimeout(() => {{
                    if (!isQualityApplied && FORCED_QUALITY !== 'auto') {{
                        console.log('[QualityController] Backup initialization triggered');
                        waitForPlayer();
                    }}
                }}, 5000);
                
                console.log('[QualityController] Enhanced Quality Controller 2024 fully initialized');
                console.log('[QualityController] Monitoring for player ready state...');
            }})();
        ";

        await _webView.AddScriptToExecuteOnDocumentCreatedAsync(script);
    }

    private async Task HideQualityControls()
    {
        if (_webView == null) return;

        var hideControlsScript = @"
            (function() {
                const hideControls = () => {
                    // Hide settings button
                    const settingsButtons = document.querySelectorAll('.ytp-settings-button');
                    settingsButtons.forEach(btn => btn.style.display = 'none');
                    
                    // Hide quality menu items when they appear
                    const qualityMenuItems = document.querySelectorAll('.ytp-menuitem');
                    qualityMenuItems.forEach(item => {
                        if (item.textContent && item.textContent.includes('Quality')) {
                            item.style.display = 'none';
                        }
                    });
                };
                
                // Apply immediately and on interval
                hideControls();
                setInterval(hideControls, 1000);
                
                // Use MutationObserver for dynamic content
                const observer = new MutationObserver(hideControls);
                observer.observe(document.body, { childList: true, subtree: true });
            })();
        ";

        await _webView.ExecuteScriptAsync(hideControlsScript);
    }

    private async Task ApplyQualitySettings()
    {
        if (_webView == null || !AppSettings.ForceVideoQuality) return;

        var forcedQuality = _qualityMappings.GetValueOrDefault(AppSettings.ForcedVideoQuality, "auto");
        
        var applyQualityScript = $@"
            (function() {{
                console.log('[QualityController] ApplyQualitySettings called with quality: {forcedQuality}');
                
                if ('{forcedQuality}' === 'auto') {{
                    console.log('[QualityController] Auto quality selected, skipping forced application');
                    return;
                }}
                
                // Enhanced quality application with multiple strategies
                let attemptCount = 0;
                const maxAttempts = 30; // Increased attempts
                let qualityApplied = false;
                
                function trySetQuality() {{
                    attemptCount++;
                    console.log('[QualityController] Quality application attempt:', attemptCount);
                    
                    // Strategy 1: Enhanced YouTube player API
                    const player = document.getElementById('movie_player') || 
                                  document.querySelector('.html5-video-player') ||
                                  document.querySelector('#player-container .html5-video-player');
                    
                    if (player && typeof player.setPlaybackQuality === 'function') {{
                        try {{
                            const availableQualities = player.getAvailableQualityLevels();
                            console.log('[QualityController] Available qualities:', availableQualities);
                            
                            if (availableQualities && availableQualities.length > 0) {{
                                // Try exact match first
                                if (availableQualities.includes('{forcedQuality}')) {{
                                    player.setPlaybackQuality('{forcedQuality}');
                                    console.log('[QualityController] Quality set successfully:', '{forcedQuality}');
                                    qualityApplied = true;
                                    return true;
                                }}
                                
                                // Try to find closest quality if exact match not available
                                const qualityOrder = ['tiny', 'small', 'medium', 'large', 'hd720', 'hd1080', 'hd1440', 'hd2160'];
                                const targetIndex = qualityOrder.indexOf('{forcedQuality}');
                                
                                if (targetIndex !== -1) {{
                                    // Look for closest available quality
                                    for (let i = 0; i < qualityOrder.length; i++) {{
                                        const checkQuality = qualityOrder[Math.abs(targetIndex - i)] || qualityOrder[targetIndex + i];
                                        if (checkQuality && availableQualities.includes(checkQuality)) {{
                                            player.setPlaybackQuality(checkQuality);
                                            console.log('[QualityController] Closest quality set:', checkQuality);
                                            qualityApplied = true;
                                            return true;
                                        }}
                                    }}
                                }}
                            }}
                        }} catch (error) {{
                            console.log('[QualityController] Player API error:', error);
                        }}
                    }}
                    
                    // Strategy 2: Global YouTube API
                    if (!qualityApplied && window.yt && window.yt.player) {{
                        try {{
                            const containers = document.querySelectorAll('#player-container, #movie_player, .html5-video-player');
                            for (const container of containers) {{
                                try {{
                                    const ytPlayer = window.yt.player.getPlayerByElement(container);
                                    if (ytPlayer && typeof ytPlayer.setPlaybackQuality === 'function') {{
                                        ytPlayer.setPlaybackQuality('{forcedQuality}');
                                        console.log('[QualityController] Quality set via YT API:', '{forcedQuality}');
                                        qualityApplied = true;
                                        return true;
                                    }}
                                }} catch (e) {{
                                    // Continue to next container
                                }}
                            }}
                        }} catch (error) {{
                            console.log('[QualityController] Global YT API error:', error);
                        }}
                    }}
                    
                    // Strategy 3: DOM manipulation as fallback
                    if (!qualityApplied && attemptCount > 10) {{
                        console.log('[QualityController] Attempting DOM manipulation fallback');
                        tryDOMManipulation();
                    }}
                    
                    // Continue trying if not successful
                    if (!qualityApplied && attemptCount < maxAttempts) {{
                        setTimeout(trySetQuality, 1000); // Slower retry for better reliability
                    }} else if (!qualityApplied) {{
                        console.log('[QualityController] Max attempts reached, quality application failed');
                    }}
                    
                    return qualityApplied;
                }}
                
                function tryDOMManipulation() {{
                    try {{
                        const settingsButton = document.querySelector('.ytp-settings-button');
                        if (!settingsButton || settingsButton.offsetParent === null) {{
                            console.log('[QualityController] Settings button not available for DOM manipulation');
                            return;
                        }}
                        
                        console.log('[QualityController] Attempting DOM manipulation');
                        settingsButton.click();
                        
                        setTimeout(() => {{
                            const qualityMenuItem = Array.from(document.querySelectorAll('.ytp-menuitem')).find(item => 
                                item.textContent && item.textContent.toLowerCase().includes('quality')
                            );
                            
                            if (qualityMenuItem) {{
                                qualityMenuItem.click();
                                
                                setTimeout(() => {{
                                    const qualityMap = {{
                                        'tiny': '144p',
                                        'small': '240p',
                                        'medium': '360p',
                                        'large': '480p',
                                        'hd720': '720p',
                                        'hd1080': '1080p',
                                        'hd1440': '1440p',
                                        'hd2160': '2160p'
                                    }};
                                    
                                    const targetText = qualityMap['{forcedQuality}'] || '{forcedQuality}';
                                    const qualityOption = Array.from(document.querySelectorAll('.ytp-menuitem')).find(item => 
                                        item.textContent && (
                                            item.textContent.includes(targetText) ||
                                            item.textContent.includes(targetText.replace('p', ''))
                                        )
                                    );
                                    
                                    if (qualityOption) {{
                                        qualityOption.click();
                                        console.log('[QualityController] Quality set via DOM manipulation:', targetText);
                                        qualityApplied = true;
                                    }}
                                    
                                    // Close menu
                                    setTimeout(() => document.body.click(), 100);
                                }}, 400);
                            }} else {{
                                // Close settings menu
                                document.body.click();
                            }}
                        }}, 400);
                        
                    }} catch (error) {{
                        console.error('[QualityController] DOM manipulation error:', error);
                        try {{ document.body.click(); }} catch (e) {{}}
                    }}
                }}
                
                // Start the quality setting process
                trySetQuality();
            }})();
        ";

        await _webView.ExecuteScriptAsync(applyQualityScript);
        
        // Log the attempt
        AppLogger.Instance.LogInfo("VideoQualityController", $"Applied quality settings: {forcedQuality}");
    }

    private void StartQualityMonitoring()
    {
        if (_disposed) return;
        
        StopQualityMonitoring();
        
        // Optimized: Reduced frequency from 3s to 8s to lower CPU usage
        _qualityMonitorTimer = new System.Timers.Timer(8000);
        _qualityMonitorTimer.Elapsed += OnQualityMonitorTick;
        _qualityMonitorTimer.AutoReset = true;
        _qualityMonitorTimer.Start();
    }

    private void StopQualityMonitoring()
    {
        _qualityMonitorTimer?.Stop();
        _qualityMonitorTimer?.Dispose();
        _qualityMonitorTimer = null;
    }

    private void OnQualityMonitorTick(object? sender, System.Timers.ElapsedEventArgs e)
    {
        if (_disposed || _webView == null) return;
        
        var now = DateTime.UtcNow;
        
        // Throttle checks to prevent excessive monitoring
        if (now - _lastQualityCheck < _minCheckInterval) return;
        
        // Use cached settings for performance
        if (!_settingsCache.ForceVideoQuality || !_settingsCache.OverrideUserQualityChanges) 
            return;

        _lastQualityCheck = now;

        try
        {
            PerformanceMonitor.Instance.MeasureOperation("VideoQualityController.MonitorTick", () =>
            {
                // Fire and forget to avoid blocking the timer
                _ = Task.Run(async () => await ApplyQualitySettings());
            });
        }
        catch (Exception ex)
        {
            AppLogger.Instance.LogError("VideoQualityController", ex, "Error in quality monitoring tick");
        }
    }

    public async Task RefreshQualitySettings()
    {
        if (_webView == null) return;

        try
        {
            // Reset the injection flag to allow re-injection
            await _webView.ExecuteScriptAsync("window.qualityControllerInjected = false;");
            
            // Re-inject the updated script
            await InjectQualityControlScript();
            
            // Apply quality settings immediately
            await ApplyQualitySettings();
            
            // Apply UI modifications
            if (AppSettings.HideQualityControls)
            {
                await HideQualityControls();
            }
            
            AppLogger.Instance.LogInfo("VideoQualityController", "Quality settings refreshed successfully");
        }
        catch (Exception ex)
        {
            AppLogger.Instance.LogError("VideoQualityController", ex, "Failed to refresh quality settings");
        }
    }

    public async Task ForceQualityApplication()
    {
        if (_webView == null) return;

        try
        {
            var forcedQuality = _qualityMappings.GetValueOrDefault(AppSettings.ForcedVideoQuality, "auto");
            
            var forceScript = $@"
                (function() {{
                    console.log('[QualityController] Force quality application triggered');
                    
                    // Reset quality application state
                    if (window.qualityControllerInjected) {{
                        window.isQualityApplied = false;
                        window.lastAppliedQuality = null;
                    }}
                    
                    // Try immediate application
                    const player = document.getElementById('movie_player') || document.querySelector('.html5-video-player');
                    if (player && typeof player.setPlaybackQuality === 'function' && '{forcedQuality}' !== 'auto') {{
                        try {{
                            player.setPlaybackQuality('{forcedQuality}');
                            console.log('[QualityController] Force applied quality:', '{forcedQuality}');
                        }} catch (error) {{
                            console.error('[QualityController] Force application error:', error);
                        }}
                    }}
                    
                    // Trigger re-initialization
                    if (typeof window.waitForPlayer === 'function') {{
                        setTimeout(() => {{
                            window.waitForPlayer();
                        }}, 500);
                    }}
                }})();
            ";
            
            await _webView.ExecuteScriptAsync(forceScript);
            AppLogger.Instance.LogInfo("VideoQualityController", $"Force quality application executed: {forcedQuality}");
        }
        catch (Exception ex)
        {
            AppLogger.Instance.LogError("VideoQualityController", ex, "Failed to force quality application");
        }
    }

    public void UpdateSettings()
    {
        if (_disposed) return;
        
        PerformanceMonitor.Instance.MeasureOperation("VideoQualityController.UpdateSettings", () =>
        {
            if (_settingsCache.ForceVideoQuality)
            {
                EnableQualityControl();
                
                if (_settingsCache.OverrideUserQualityChanges)
                {
                    StartQualityMonitoring();
                }
                else
                {
                    StopQualityMonitoring();
                }
            }
            else
            {
                DisableQualityControl();
            }
        });
    }

    public void Dispose()
    {
        if (_disposed) return;
        
        _disposed = true;
        
        StopQualityMonitoring();
        
        if (_webView != null)
        {
            try
            {
                _webView.DOMContentLoaded -= OnDOMContentLoaded;
                _webView.NavigationCompleted -= OnNavigationCompleted;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error disposing VideoQualityController: {ex.Message}");
            }
        }
        
        _webView = null;
    }

    public static string[] GetAvailableQualities()
    {
        return new[] { "auto", "144p", "240p", "360p", "480p", "720p", "1080p", "1440p", "2160p" };
    }

    public static string GetQualityDisplayName(string quality)
    {
        return quality switch
        {
            "auto" => "Auto (YouTube Default)",
            "144p" => "144p (Lowest)",
            "240p" => "240p (Low)",
            "360p" => "360p (Medium)",
            "480p" => "480p (Standard)",
            "720p" => "720p (HD)",
            "1080p" => "1080p (Full HD)",
            "1440p" => "1440p (2K)",
            "2160p" => "2160p (4K)",
            _ => quality
        };
    }
}
