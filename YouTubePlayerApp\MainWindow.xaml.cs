using System;
using System.Diagnostics;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Threading;
using Microsoft.Web.WebView2.Core;
using YouTubePlayerApp.Core;

namespace YouTubePlayerApp;

/// <summary>
/// Interaction logic for MainWindow.xaml
/// </summary>
public partial class MainWindow : Window
{
    private bool isFullScreen = false;
    private WindowState previousWindowState;
    private WindowStyle previousWindowStyle;
    private WebViewManager? _webViewManager;
    private readonly DispatcherTimer _statsUpdateTimer;
    private readonly DateTime _applicationStartTime;

    public MainWindow()
    {
        _applicationStartTime = DateTime.Now;
        InitializeComponent();
        InitializeWebView();
        
        // Set up stats update timer
        _statsUpdateTimer = new DispatcherTimer
        {
            Interval = TimeSpan.FromSeconds(3)
        };
        _statsUpdateTimer.Tick += UpdateStatsDisplay;
        _statsUpdateTimer.Start();
        
        // Initialize performance monitoring
        PerformanceMonitor.Instance.LogCurrentState("Application Start");
    }

    protected override void OnClosed(EventArgs e)
    {
        try
        {
            // Dispose resources to prevent memory leaks
            _webViewManager?.Dispose();
            PerformanceMonitor.Instance.Dispose();
            SettingsCache.Instance.Dispose();
            
            PerformanceMonitor.Instance.LogCurrentState("Application Exit");
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"Error during cleanup: {ex.Message}");
        }
        
        // Stop timers and clean up resources
        _statsUpdateTimer?.Stop();
        
        base.OnClosed(e);
    }

    private async void InitializeWebView()
    {
        PerformanceMonitor.Instance.StartOperation("WebView.Initialize");
        try
        {
            await YouTubeWebView.EnsureCoreWebView2Async();
            _webViewManager = new WebViewManager(YouTubeWebView.CoreWebView2);
            await _webViewManager.InitializeAsync();
        }
        catch (Exception ex)
        {
            AppLogger.Instance.LogError("MainWindow", ex, "Failed to initialize WebView.");
            MessageBox.Show($"A critical error occurred while initializing the web view: {ex.Message}", "Initialization Error", MessageBoxButton.OK, MessageBoxImage.Error);
        }
        finally
        {
            PerformanceMonitor.Instance.EndOperation("WebView.Initialize");
        }
    }

    private void WebView_NavigationCompleted(object sender, CoreWebView2NavigationCompletedEventArgs e)
    {
        if (e.IsSuccess)
        {
            Title = $"YouTube Desktop Player - {YouTubeWebView.CoreWebView2.DocumentTitle}";
            UpdateAdBlockStatus();
        }
    }

    private void UpdateAdBlockStatus()
    {
        // This method is now handled by UpdateStatsDisplay
        // Keep for backward compatibility if needed
        if (AppSettings.ShowAdBlockStats && AppSettings.BlockAds && _webViewManager?.AdBlocker != null)
        {
            var totalBlocked = _webViewManager.AdBlocker.GetTotalBlocked();
            var stats = _webViewManager.AdBlocker.GetBlockedStats();
            
            SessionTotalText.Text = $"Session: {totalBlocked}";
            BlockedRequestsText.Text = $"Requests: {stats.GetValueOrDefault("Requests", 0)}";
            AdBlockStatusIndicator.Visibility = Visibility.Visible;
        }
        else
        {
            AdBlockStatusIndicator.Visibility = Visibility.Collapsed;
        }
    }

    // Navigation Menu Handlers
    private void Back_Click(object sender, RoutedEventArgs e)
    {
        if (YouTubeWebView.CoreWebView2.CanGoBack)
            YouTubeWebView.CoreWebView2.GoBack();
    }

    private void Forward_Click(object sender, RoutedEventArgs e)
    {
        if (YouTubeWebView.CoreWebView2.CanGoForward)
            YouTubeWebView.CoreWebView2.GoForward();
    }

    private void Refresh_Click(object sender, RoutedEventArgs e)
    {
        YouTubeWebView.CoreWebView2.Reload();
    }

    private void Home_Click(object sender, RoutedEventArgs e)
    {
        YouTubeWebView.CoreWebView2.Navigate("https://www.youtube.com");
    }

    // View Menu Handlers
    private void FullScreen_Click(object sender, RoutedEventArgs e)
    {
        ToggleFullScreen();
    }

    private void ZoomIn_Click(object sender, RoutedEventArgs e)
    {
        YouTubeWebView.ZoomFactor += 0.1;
    }

    private void ZoomOut_Click(object sender, RoutedEventArgs e)
    {
        YouTubeWebView.ZoomFactor = Math.Max(0.1, YouTubeWebView.ZoomFactor - 0.1);
    }

    private void ResetZoom_Click(object sender, RoutedEventArgs e)
    {
        YouTubeWebView.ZoomFactor = 1.0;
    }

    private void Settings_Click(object sender, RoutedEventArgs e)
    {
        var settingsWindow = new SettingsWindow();
        settingsWindow.Owner = this;
        settingsWindow.ShowDialog();
    }

    private void Performance_Click(object sender, RoutedEventArgs e)
    {
        var performanceWindow = new PerformanceBenchmarkWindow();
        performanceWindow.Owner = this;
        performanceWindow.Show();
    }

    private void MemoryProfiler_Click(object sender, RoutedEventArgs e)
    {
        var memoryProfilerWindow = new MemoryProfilerWindow();
        memoryProfilerWindow.Owner = this;
        memoryProfilerWindow.Show();
    }

    private void LogViewer_Click(object sender, RoutedEventArgs e)
    {
        var logViewerWindow = new LogViewerWindow();
        logViewerWindow.Owner = this;
        logViewerWindow.Show();
    }

    private async void DiagnoseAdBlocking_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            var diagnostics = await AdBlockDiagnostics.RunComprehensiveDiagnosticsAsync(YouTubeWebView.CoreWebView2);
            
            var diagnosticsWindow = new Window
            {
                Title = "Ad Blocking Diagnostics",
                Width = 900,
                Height = 700,
                Owner = this,
                WindowStartupLocation = WindowStartupLocation.CenterOwner
            };
            
            var scrollViewer = new ScrollViewer
            {
                VerticalScrollBarVisibility = ScrollBarVisibility.Auto,
                HorizontalScrollBarVisibility = ScrollBarVisibility.Auto,
                Padding = new Thickness(10)
            };
            
            var textBlock = new TextBlock
            {
                Text = diagnostics,
                FontFamily = new FontFamily("Consolas"),
                FontSize = 11,
                TextWrapping = TextWrapping.Wrap
            };
            
            scrollViewer.Content = textBlock;
            diagnosticsWindow.Content = scrollViewer;
            diagnosticsWindow.ShowDialog();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"Error running ad blocking diagnostics: {ex.Message}", "Diagnostics Error", 
                          MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private async void ForceAdRemoval_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            var result = await AdBlockDiagnostics.ForceAdRemovalAsync(YouTubeWebView.CoreWebView2);
            MessageBox.Show(result, "Force Ad Removal", MessageBoxButton.OK, MessageBoxImage.Information);
        }
        catch (Exception ex)
        {
            MessageBox.Show($"Error during force ad removal: {ex.Message}", "Force Removal Error", 
                          MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private async void TestAdBlocking_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            var isEffective = await AdBlockDiagnostics.TestAdBlockingEffectivenessAsync(YouTubeWebView.CoreWebView2);
            var message = isEffective 
                ? "✅ Ad blocking is working effectively! No ads detected."
                : "⚠️ Ad blocking may not be fully effective. Some ads were detected.";
            
            MessageBox.Show(message, "Ad Blocking Test", MessageBoxButton.OK, 
                          isEffective ? MessageBoxImage.Information : MessageBoxImage.Warning);
        }
        catch (Exception ex)
        {
            MessageBox.Show($"Error testing ad blocking: {ex.Message}", "Test Error", 
                          MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private async void DiagnoseVideoPlayback_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            var diagnostics = await VideoPlaybackDiagnostics.RunVideoPlaybackTestAsync(YouTubeWebView.CoreWebView2);
            
            var diagnosticsWindow = new Window
            {
                Title = "Video Playback Diagnostics",
                Width = 1000,
                Height = 800,
                Owner = this,
                WindowStartupLocation = WindowStartupLocation.CenterOwner
            };
            
            var scrollViewer = new ScrollViewer
            {
                VerticalScrollBarVisibility = ScrollBarVisibility.Auto,
                HorizontalScrollBarVisibility = ScrollBarVisibility.Auto,
                Padding = new Thickness(10)
            };
            
            var textBlock = new TextBlock
            {
                Text = diagnostics,
                FontFamily = new FontFamily("Consolas"),
                FontSize = 11,
                TextWrapping = TextWrapping.Wrap
            };
            
            scrollViewer.Content = textBlock;
            diagnosticsWindow.Content = scrollViewer;
            diagnosticsWindow.ShowDialog();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"Error running video playback diagnostics: {ex.Message}", "Diagnostics Error", 
                          MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private async void FixVideoPlayback_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            var result = await VideoPlaybackDiagnostics.FixVideoPlaybackIssuesAsync(YouTubeWebView.CoreWebView2);
            MessageBox.Show(result, "Video Playback Fix", MessageBoxButton.OK, MessageBoxImage.Information);
        }
        catch (Exception ex)
        {
            MessageBox.Show($"Error fixing video playback: {ex.Message}", "Fix Error", 
                          MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private async void TestVideoPlayback_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            var isWorking = await VideoPlaybackDiagnostics.TestVideoPlaybackFunctionalityAsync(YouTubeWebView.CoreWebView2);
            var message = isWorking 
                ? "✅ Video playback is working perfectly! All controls are functional."
                : "⚠️ Video playback issues detected. Some functionality may be impaired.";
            
            MessageBox.Show(message, "Video Playback Test", MessageBoxButton.OK, 
                          isWorking ? MessageBoxImage.Information : MessageBoxImage.Warning);
        }
        catch (Exception ex)
        {
            MessageBox.Show($"Error testing video playback: {ex.Message}", "Test Error", 
                          MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private async void RunFullTestSuite_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            var testResults = await ComprehensiveTestSuite.RunFullTestSuiteAsync(YouTubeWebView.CoreWebView2);
            
            var testWindow = new Window
            {
                Title = "Comprehensive Test Suite Results",
                Width = 1100,
                Height = 900,
                Owner = this,
                WindowStartupLocation = WindowStartupLocation.CenterOwner
            };
            
            var scrollViewer = new ScrollViewer
            {
                VerticalScrollBarVisibility = ScrollBarVisibility.Auto,
                HorizontalScrollBarVisibility = ScrollBarVisibility.Auto,
                Padding = new Thickness(15)
            };
            
            var textBlock = new TextBlock
            {
                Text = testResults,
                FontFamily = new FontFamily("Consolas"),
                FontSize = 11,
                TextWrapping = TextWrapping.Wrap,
                LineHeight = 16
            };
            
            scrollViewer.Content = textBlock;
            testWindow.Content = scrollViewer;
            testWindow.ShowDialog();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"Error running comprehensive test suite: {ex.Message}", "Test Suite Error", 
                          MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private async void QuickHealthCheck_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            var healthCheck = await ComprehensiveTestSuite.RunQuickHealthCheckAsync(YouTubeWebView.CoreWebView2);
            MessageBox.Show(healthCheck, "Quick Health Check", MessageBoxButton.OK, MessageBoxImage.Information);
        }
        catch (Exception ex)
        {
            MessageBox.Show($"Error running health check: {ex.Message}", "Health Check Error", 
                          MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private async void DiagnoseVideo_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            var diagnostics = await VideoInteractionDiagnostics.DiagnoseVideoInteractionAsync(YouTubeWebView.CoreWebView2);
            
            var diagnosticsWindow = new Window
            {
                Title = "Video Interaction Diagnostics",
                Width = 800,
                Height = 600,
                Owner = this,
                WindowStartupLocation = WindowStartupLocation.CenterOwner
            };
            
            var scrollViewer = new ScrollViewer
            {
                VerticalScrollBarVisibility = ScrollBarVisibility.Auto,
                HorizontalScrollBarVisibility = ScrollBarVisibility.Auto,
                Padding = new Thickness(10)
            };
            
            var textBlock = new TextBlock
            {
                Text = diagnostics,
                FontFamily = new FontFamily("Consolas"),
                FontSize = 12,
                TextWrapping = TextWrapping.Wrap
            };
            
            scrollViewer.Content = textBlock;
            diagnosticsWindow.Content = scrollViewer;
            diagnosticsWindow.ShowDialog();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"Error running diagnostics: {ex.Message}", "Diagnostics Error", 
                          MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private async void FixVideoInteraction_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            var result = await VideoInteractionDiagnostics.AttemptVideoInteractionFixAsync(YouTubeWebView.CoreWebView2);
            
            if (result)
            {
                MessageBox.Show("Video interaction fixes applied successfully!\n\nTry clicking on videos now.", 
                              "Fix Applied", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            else
            {
                MessageBox.Show("No fixes were applied. The video interaction might already be working correctly.", 
                              "No Fixes Needed", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"Error applying fixes: {ex.Message}", "Fix Error", 
                          MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void UpdateStatsDisplay(object? sender, EventArgs e)
    {
        try
        {
            // Update ad blocking statistics
            if (_webViewManager?.AdBlocker != null)
            {
                var stats = _webViewManager.AdBlocker.GetBlockedStats();
                var totalBlocked = _webViewManager.AdBlocker.GetTotalBlocked();
                
                Dispatcher.Invoke(() =>
                {
                    BlockedRequestsText.Text = $"Requests: {stats.GetValueOrDefault("Requests", 0)}";
                    BlockedElementsText.Text = $"Elements: {stats.GetValueOrDefault("Elements", 0)}";
                    SessionTotalText.Text = $"Session: {totalBlocked}";
                    
                    // Update status indicator color based on activity
                    if (totalBlocked > 0)
                    {
                        AdBlockerStatusIndicator.Fill = System.Windows.Media.Brushes.LimeGreen;
                    }
                    else
                    {
                        AdBlockerStatusIndicator.Fill = System.Windows.Media.Brushes.Orange;
                    }
                });
            }
            
            // Update performance metrics
            if (AppSettings.ShowPerformanceIndicator)
            {
                var metrics = PerformanceMonitor.Instance.GetCurrentMetrics();
                var uptime = DateTime.Now - _applicationStartTime;
                
                Dispatcher.Invoke(() =>
                {
                    MemoryUsageText.Text = $"Memory: {metrics.CurrentMemoryMB:F0} MB";
                    UptimeText.Text = $"Uptime: {uptime.TotalMinutes:F0}m";
                    PerformanceIndicator.Visibility = Visibility.Visible;
                });
            }
            else
            {
                Dispatcher.Invoke(() =>
                {
                    PerformanceIndicator.Visibility = Visibility.Collapsed;
                });
            }
        }
        catch (Exception ex)
        {
            AppLogger.Instance.LogError("MainWindow", ex, "Error updating stats display");
        }
    }

    private void ToggleFullScreen()
    {
        if (!isFullScreen)
        {
            previousWindowState = WindowState;
            previousWindowStyle = WindowStyle;
            
            WindowStyle = WindowStyle.None;
            WindowState = WindowState.Maximized;
            isFullScreen = true;
        }
        else
        {
            WindowStyle = previousWindowStyle;
            WindowState = previousWindowState;
            isFullScreen = false;
        }
    }

    // Keyboard shortcuts
    protected override void OnKeyDown(KeyEventArgs e)
    {
        if (e.Key == Key.F11)
        {
            ToggleFullScreen();
            e.Handled = true;
        }
        else if (e.Key == Key.F5)
        {
            YouTubeWebView.CoreWebView2.Reload();
            e.Handled = true;
        }
        
        base.OnKeyDown(e);
    }

    // Toggle ad blocker from settings
    public void ToggleAdBlocker(bool enable)
    {
        if (_webViewManager?.AdBlocker != null)
        {
            if (enable)
            {
                _webViewManager.AdBlocker.EnableAdBlocking();
            }
            else
            {
                _webViewManager.AdBlocker.DisableAdBlocking();
            }
        }
    }

    // Public method to access the video quality controller
    public VideoQualityController? GetQualityController()
    {
        return _webViewManager?.QualityController;
    }

    // Public method to access the video-safe ad blocker
    public VideoSafeAdBlocker? GetVideoSafeAdBlocker()
    {
        return _webViewManager?.AdBlocker;
    }

    // Public method to access the YouTube enhancer
    public YouTubeEnhancer? GetYouTubeEnhancer()
    {
        return _webViewManager?.Enhancer;
    }

    // Update quality settings from settings window
    public async Task UpdateQualitySettings()
    {
        if (_webViewManager?.QualityController != null)
        {
            _webViewManager.QualityController.UpdateSettings();
            await _webViewManager.QualityController.RefreshQualitySettings();
            
            // Force immediate application if quality forcing is enabled
            if (AppSettings.ForceVideoQuality)
            {
                await _webViewManager.QualityController.ForceQualityApplication();
            }
        }
        
        // Also update YouTube enhancer settings
        if (_webViewManager?.Enhancer != null)
        {
            await _webViewManager.Enhancer.UpdateSettings();
        }
    }

    // Force quality application (can be called from settings or manually)
    public async Task ForceQualityApplication()
    {
        if (_webViewManager?.QualityController != null)
        {
            await _webViewManager.QualityController.ForceQualityApplication();
        }
    }
}
